package com.example;

import com.example.util.StringUtils;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;

public class QuickStart {

    public static void main(String[] args) {
        System.out.println(Strings.class);
        System.out.println(Lists.class);

        // Use Helper class from the same package
        String result = Helper.processString("test");
        System.out.println(result);

        // Use StringUtils from different package
        StringUtils.processLists();
    }
}
